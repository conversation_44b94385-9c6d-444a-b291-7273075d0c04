<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Videos - Essential Property Services</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Merriweather:ital,wght@0,400;0,700;1,400&display=swap"
      rel="stylesheet"
    />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#4B0082",
              accent: "#FFD700",
            },
            fontFamily: {
              sans: ['"Inter"', "system-ui", "-apple-system", "sans-serif"],
              serif: ['"Merriweather"', "Georgia", "serif"],
            },
          },
        },
      };
    </script>
    <style>
      html {
        scroll-behavior: smooth;
      }
      body {
        font-family: "Inter", system-ui, -apple-system, sans-serif;
      }

      .hide-scrollbar::-webkit-scrollbar {
        display: none;
      }

      .hide-scrollbar {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    </style>

  </head>
  <body class="font-sans text-gray-800">
    <!-- Header -->
    <header
      class="fixed w-full z-50 transition-all duration-300 py-4 bg-white shadow-md"
      id="header"
    >
      <div class="container mx-auto px-4 md:px-6">
        <div class="flex justify-between items-center">
          <div class="flex items-center">
            <a href="index.html">
              <img
                src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681586cfdb0184d1a887b9de.svg"
                alt="Essential Property Services Logo"
                class="h-12 md:h-16"
              />
            </a>
          </div>

          <div class="hidden md:flex items-center space-x-8">
            <nav>
              <ul class="flex space-x-6">
                <li>
                  <a
                    href="services.html"
                    class="nav-link font-medium hover:text-primary transition text-gray-800"
                    >Services</a
                  >
                </li>
                <li>
                  <a
                    href="portfolio.html"
                    class="nav-link font-medium hover:text-primary transition text-gray-800"
                    >Portfolio</a
                  >
                </li>
                <li>
                  <a
                    href="team.html"
                    class="nav-link font-medium hover:text-primary transition text-gray-800"
                    >Our Team</a
                  >
                </li>
                <li>
                  <a
                    href="index.html#contact"
                    class="nav-link font-medium hover:text-primary transition text-gray-800"
                    >Contact</a
                  >
                </li>
              </ul>
            </nav>

            <a
              href="tel:9076009900"
              class="flex items-center bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md font-medium transition"
            >
              <svg
                class="w-[18px] h-[18px] mr-2"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                />
              </svg>
              (*************
            </a>
          </div>

          <button class="md:hidden text-primary" onclick="toggleMobileMenu()">
            <svg
              class="w-7 h-7"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <line x1="3" y1="12" x2="21" y2="12"></line>
              <line x1="3" y1="6" x2="21" y2="6"></line>
              <line x1="3" y1="18" x2="21" y2="18"></line>
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile menu -->
      <div id="mobileMenu" class="hidden md:hidden bg-white shadow-lg">
        <nav class="container mx-auto px-4 py-4">
          <ul class="space-y-4">
            <li>
              <a
                href="services.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Services
              </a>
            </li>
            <li>
              <a
                href="portfolio.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Portfolio
              </a>
            </li>
            <li>
              <a
                href="team.html"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Our Team
              </a>
            </li>
            <li>
              <a
                href="index.html#contact"
                class="block py-2 text-gray-800 font-medium hover:text-primary"
                onclick="closeMobileMenu()"
              >
                Contact
              </a>
            </li>
            <li>
              <a
                href="tel:9076009900"
                class="flex items-center w-full bg-primary hover:bg-primary/90 text-white px-4 py-3 rounded-md font-medium justify-center transition"
                onclick="closeMobileMenu()"
              >
                <svg
                  class="w-[18px] h-[18px] mr-2"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                >
                  <path
                    d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                  />
                </svg>
                (*************
              </a>
            </li>
          </ul>
        </nav>
      </div>
    </header>

    <div class="min-h-screen bg-gray-50 pt-32">
      <!-- Videos Grid -->
      <section class="py-12">
        <div class="container mx-auto px-4">
          <h1 class="text-4xl font-bold text-center mb-4 text-primary">
            Our Transformation Videos
          </h1>
          <p class="text-center text-gray-600 mb-12 max-w-2xl mx-auto">
            Watch our latest project transformations and get inspired for your
            next home improvement project. Our videos showcase our craftsmanship
            and attention to detail.
          </p>
          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            id="videosGrid"
          >
            <!-- Videos will be dynamically inserted here -->
          </div>
        </div>
      </section>
    </div>

    <script type="module" src="/src/videos.js"></script>
  </body>
  <!-- Footer -->
  <footer class="bg-primary text-white py-12">
    <div class="container mx-auto px-4 md:px-6">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
        <div>
          <img
            src="https://storage.googleapis.com/msgsndr/ZvYXEcu0DmkCtuv5cGcG/media/681586cfdb0184d1a887b9de.svg"
            alt="Essential Property Services Logo"
            class="h-48 mb-4"
          />
          <p class="text-gray-300 mb-4">
            Created to serve with excellence, through humility and
            professionalism.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="text-accent hover:text-accent/80 transition">
              <span class="sr-only">Facebook</span>
              <svg
                class="w-6 h-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
            </a>
            <a href="#" class="text-accent hover:text-accent/80 transition">
              <span class="sr-only">Instagram</span>
              <svg
                class="w-6 h-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                ></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
            </a>
          </div>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-4">Contact Information</h3>
          <ul class="space-y-3">
            <li class="flex items-center">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"
                ></path>
              </svg>
              <a href="tel:9076009900" class="hover:text-accent transition"
                >(*************</a
              >
            </li>
            <li class="flex items-center">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path
                  d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                ></path>
                <polyline points="22,6 12,13 2,6"></polyline>
              </svg>
              <a
                href="mailto:<EMAIL>"
                class="hover:text-accent transition"
                ><EMAIL></a
              >
            </li>
            <li class="flex items-start">
              <svg
                class="w-[18px] h-[18px] mr-3 text-accent mt-1"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
              >
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                <circle cx="12" cy="10" r="3"></circle>
              </svg>
              <span>Serving Anchorage, AK & Surrounding Areas</span>
            </li>
          </ul>
        </div>

        <div>
          <h3 class="text-lg font-semibold mb-4">Licensing & Information</h3>
          <ul class="space-y-2 text-gray-300">
            <li>Alaska General Contractor License 217482</li>
            <li>MOA License CON14200</li>
            <li>Fully Insured and Bonded</li>
            <li>Available 24/7/364</li>
          </ul>
        </div>
      </div>

      <div
        class="border-t border-gray-700 pt-8 mt-8 text-gray-300 text-sm text-center"
      >
        <p>
          ©
          <script>
            document.write(new Date().getFullYear());
          </script>
          Essential Property Services LLC. All rights reserved.
        </p>
        <p class="mt-2">
          <a href="#" class="hover:text-accent transition">Privacy Policy</a>
          {' | '}
          <a href="#" class="hover:text-accent transition">Terms of Service</a>
        </p>
      </div>
    </div>
  </footer>
</html>
