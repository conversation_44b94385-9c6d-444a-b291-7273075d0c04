# Essential Property Services Website

A professional website for Essential Property Services LLC, showcasing comprehensive property maintenance, repair, and remodeling services in Alaska.

## 🏠 About

Essential Property Services LLC is a full-service property maintenance company offering:
- Emergency services (plumbing, electrical, structural)
- Property maintenance and repairs
- Kitchen and bathroom remodeling
- Licensed plumbing and electrical work
- Handyman services
- Outdoor living projects

## 🚀 Live Website

Visit the live website: [Essential Property Services](https://your-domain.com)

## 🛠 Technologies Used

- **HTML5** - Semantic markup and structure
- **CSS3** - Styling with Tailwind CSS framework
- **JavaScript** - Interactive functionality
- **Vite** - Build tool and development server
- **Tailwind CSS** - Utility-first CSS framework
- **Responsive Design** - Mobile-first approach

## 📁 Project Structure

```
001-epsak/
├── src/                    # Source files
│   ├── main.js            # Main JavaScript file with projects data
│   ├── videos.js          # Instagram videos data
│   ├── style.css          # Custom styles
│   └── resources/         # Additional resources
├── public/                # Public assets
├── dist/                  # Built files for production
├── index.html             # Home page
├── services.html          # Services page with image galleries
├── portfolio.html         # Portfolio/projects showcase
├── videos.html            # Video content page
├── team.html              # Team information
├── contact-alt.html       # Alternative contact page
├── privacy.html           # Privacy policy
├── terms.html             # Terms of service
├── package.json           # Node.js dependencies
├── vite.config.js         # Vite configuration
└── netlify.toml           # Netlify deployment config
```

## 🎨 Key Features

### Home Page
- Hero section with company branding
- Service overview cards with hover effects
- Customer testimonials
- Call-to-action sections

### Services Page
- Comprehensive service listings
- Image galleries for different service categories:
  - Emergency Services
  - Electrical Services (12 professional images)
  - Plumbing Services
  - Maintenance Services
- Detailed service descriptions
- Pricing information

### Portfolio Page
- Before/after project showcases
- Customer testimonials
- Project categories and filtering

### Videos Page
- Instagram video integration
- Service demonstration videos
- Social media content

## 🚀 Getting Started

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn package manager

### Installation

1. Clone the repository:
```bash
git clone https://github.com/mvalencia464/001-epsak.git
cd 001-epsak
```

2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

### Build for Production

```bash
npm run build
```

The built files will be in the `dist/` directory.

## 📱 Responsive Design

The website is fully responsive and optimized for:
- Mobile devices (320px+)
- Tablets (768px+)
- Desktop computers (1024px+)
- Large screens (1280px+)

## 🖼 Image Management

Images are hosted on Google Cloud Storage and optimized through LeadConnector:
- WebP format for better compression
- Responsive image sizing
- CDN delivery for fast loading

### Image Categories
- **Service galleries**: Professional work photos
- **Before/after**: Project transformations
- **Team photos**: Staff and company images
- **Hero images**: High-impact landing visuals

## 🎯 SEO Optimization

- Semantic HTML structure
- Meta tags and descriptions
- Alt text for all images
- Fast loading times
- Mobile-friendly design

## 🚀 Deployment

### Netlify (Current)
The site is configured for Netlify deployment with:
- Automatic builds from GitHub
- Custom redirects and headers
- Form handling capabilities

### Manual Deployment
1. Build the project: `npm run build`
2. Upload the `dist/` folder to your hosting provider
3. Configure your domain and SSL

## 🔧 Development

### Adding New Images
1. Upload images to Google Cloud Storage
2. Get the LeadConnector optimized URL
3. Add to the appropriate gallery in HTML files
4. Include descriptive alt text and captions

### Modifying Services
- Edit service data in `src/main.js`
- Update service galleries in `services.html`
- Maintain consistent styling and structure

### Updating Content
- Project data: `src/main.js`
- Video content: `src/videos.js`
- Static pages: Edit HTML files directly

## 📞 Contact Information

**Essential Property Services LLC**
- Phone: [Your Phone Number]
- Email: [Your Email]
- Location: Alaska
- Website: [Your Website URL]

## 📄 License

This project is proprietary and confidential. All rights reserved by Essential Property Services LLC.

## 🤝 Contributing

This is a private project for Essential Property Services LLC. For any updates or modifications, please contact the development team.

---

**Last Updated**: June 2025
**Version**: 1.0.0
